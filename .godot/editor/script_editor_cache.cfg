[res://scripts/MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 39,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/GothicDialogueBalloon.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 113,
"scroll_position": 88.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/Chapter.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 4,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 1080,
"scroll_position": 1055.0,
"selection": true,
"selection_from_column": 0,
"selection_from_line": 1051,
"selection_to_column": 4,
"selection_to_line": 1080,
"syntax_highlighter": "GDScript"
}
