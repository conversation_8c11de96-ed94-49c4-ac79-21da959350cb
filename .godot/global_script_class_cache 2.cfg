list=[{
"base": &"Node2D",
"class": &"BaseDialogueTestScene",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/test_scene.gd"
}, {
"base": &"Control",
"class": &"BloodInscriptionPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/BloodInscriptionPuzzle.gd"
}, {
"base": &"Control",
"class": &"CaesarCipherPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/CaesarCipherPuzzle.gd"
}, {
"base": &"Control",
"class": &"Chapter",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/Chapter.gd"
}, {
"base": &"Node",
"class": &"DMCache",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/utilities/dialogue_cache.gd"
}, {
"base": &"CodeEdit",
"class": &"DMCodeEdit",
"icon": "",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/components/code_edit.gd"
}, {
"base": &"RefCounted",
"class": &"DMCompilation",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/compilation.gd"
}, {
"base": &"RefCounted",
"class": &"DMCompiledLine",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/compiled_line.gd"
}, {
"base": &"RefCounted",
"class": &"DMCompiler",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/compiler.gd"
}, {
"base": &"RefCounted",
"class": &"DMCompilerRegEx",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/compiler_regex.gd"
}, {
"base": &"RefCounted",
"class": &"DMCompilerResult",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/compiler_result.gd"
}, {
"base": &"RefCounted",
"class": &"DMConstants",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/constants.gd"
}, {
"base": &"EditorExportPlugin",
"class": &"DMExportPlugin",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/export_plugin.gd"
}, {
"base": &"RefCounted",
"class": &"DMExpressionParser",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/expression_parser.gd"
}, {
"base": &"EditorImportPlugin",
"class": &"DMImportPlugin",
"icon": "",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/import_plugin.gd"
}, {
"base": &"EditorInspectorPlugin",
"class": &"DMInspectorPlugin",
"icon": "",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/inspector_plugin.gd"
}, {
"base": &"RefCounted",
"class": &"DMResolvedGotoData",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/resolved_goto_data.gd"
}, {
"base": &"RefCounted",
"class": &"DMResolvedLineData",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/resolved_line_data.gd"
}, {
"base": &"RefCounted",
"class": &"DMResolvedTagData",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/resolved_tag_data.gd"
}, {
"base": &"Node",
"class": &"DMSettings",
"icon": "",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/settings.gd"
}, {
"base": &"SyntaxHighlighter",
"class": &"DMSyntaxHighlighter",
"icon": "",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/components/code_edit_syntax_highlighter.gd"
}, {
"base": &"EditorTranslationParserPlugin",
"class": &"DMTranslationParserPlugin",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/editor_translation_parser_plugin.gd"
}, {
"base": &"RefCounted",
"class": &"DMTreeLine",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/compiler/tree_line.gd"
}, {
"base": &"RichTextLabel",
"class": &"DialogueLabel",
"icon": "res://addons/dialogue_manager/assets/icon.svg",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/dialogue_label.gd"
}, {
"base": &"RefCounted",
"class": &"DialogueLine",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/dialogue_line.gd"
}, {
"base": &"CanvasLayer",
"class": &"DialogueManagerExampleBalloon",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/example_balloon/example_balloon.gd"
}, {
"base": &"Resource",
"class": &"DialogueResource",
"icon": "res://addons/dialogue_manager/assets/icon.svg",
"is_abstract": false,
"is_tool": true,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/dialogue_resource.gd"
}, {
"base": &"RefCounted",
"class": &"DialogueResponse",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/dialogue_response.gd"
}, {
"base": &"Container",
"class": &"DialogueResponsesMenu",
"icon": "res://addons/dialogue_manager/assets/responses_menu.svg",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://addons/dialogue_manager/dialogue_responses_menu.gd"
}, {
"base": &"Control",
"class": &"DialogueSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/DialogueSystem.gd"
}, {
"base": &"RefCounted",
"class": &"FontLoader",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/font_loader.gd"
}, {
"base": &"Control",
"class": &"MemoryTestPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/MemoryTestPuzzle.gd"
}, {
"base": &"Control",
"class": &"NavigationPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/NavigationPuzzle.gd"
}, {
"base": &"Control",
"class": &"OrderTestPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/OrderTestPuzzle.gd"
}, {
"base": &"Control",
"class": &"ReversedMessagePuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ReversedMessagePuzzle.gd"
}, {
"base": &"Control",
"class": &"RitualRhythmPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/RitualRhythmPuzzle.gd"
}, {
"base": &"Control",
"class": &"ShadowCodePuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ShadowCodePuzzle.gd"
}, {
"base": &"Control",
"class": &"SimpleCalculationPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/SimpleCalculationPuzzle.gd"
}, {
"base": &"Control",
"class": &"ThreeLeverssPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ThreeLeverssPuzzle.gd"
}, {
"base": &"Control",
"class": &"ThreeSistersPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ThreeSistersPuzzle.gd"
}, {
"base": &"Control",
"class": &"VampireArithmeticPuzzle",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/VampireArithmeticPuzzle.gd"
}, {
"base": &"Control",
"class": &"VirtualKeyboard",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/VirtualKeyboard.gd"
}]
