extends Node

# Singleton pre riadenie hry
signal chapter_completed(chapter_number: int)
signal puzzle_completed(chapter_number: int, puzzle_number: int)
signal item_collected(item_id: String)
signal item_used(item_id: String)

# Stav hry
var current_chapter: int = 1
var story_phase: int = 0  # Aktuálna fáza príbehu v kapitole
var completed_chapters: Array[int] = []
var completed_puzzles: Dictionary = {} # {chapter_number: [puzzle1, puzzle2]}
var last_played_chapter: int = 1
var last_story_phase: int = 0
var last_dialogue_index: int = 0
var current_game_state: String = "story"  # "story", "dialogue", "puzzle"
var current_puzzle_name: String = ""
var current_dialogue_data: Array = []
var is_continuing_game: bool = false  # Flag pre rozlíšenie nová hra vs pokračovanie

# Inventory systém
var collected_items: Array = []  # ID predmetov ktoré hráč získal
var used_items: Array = []  # ID predmetov ktoré hráč použil
var inventory_visible: bool = false
var game_settings: Dictionary = {
	"master_volume": 1.0,
	"music_volume": 1.0,
	"sfx_volume": 1.0
}

# Informácie o kapitolách
var chapter_info: Dictionary = {
	1: {
		"title": "Kapitola 1: Záhadný začiatok",
		"description": "Marec 1894. Cesta k zámku Van Helsinga cez karpatské horstvo. Rozlúštite Van Helsingove šifry a nájdite cestu k zámku.",
		"puzzles": ["Van Helsingova šifra", "Cesta lesom"]
	},
	2: {
		"title": "Kapitola 2: Hlbšie do temnoty",
		"description": "Brána zámku. Dokážte, že patríte k Rádu a vstúpte do Van Helsingovho sídla.",
		"puzzles": ["Krvavý nápis", "Skúška Rádu"]
	},
	3: {
		"title": "Kapitola 3: Pátranie v zámku",
		"description": "Vstúpte do Van Helsingovho zámku a nájdite stopy po jeho zmiznutí.",
		"puzzles": ["Obrátená správa", "Jednoduchý výpočet"]
	},
	4: {
		"title": "Kapitola 4: Tajné krídlo",
		"description": "Staré krídlo zámku plné pascí a alchymistických tajomstiev.",
		"puzzles": ["Pamäťový test", "Vampírska aritmetika"]
	},
	5: {
		"title": "Kapitola 5: Krypty",
		"description": "Zostup do pradávnych krypt plných tajomstiev a nebezpečenstiev.",
		"puzzles": ["Kód z tieňov", "Tri páky"]
	},
	6: {
		"title": "Kapitola 6: Konfrontácia",
		"description": "Finálny súboj s grófkou Isabelle Báthoryovou. Osud sveta je vo vašich rukách.",
		"puzzles": ["Tri sestry", "Rytmus rituálu"]
	},
	7: {
		"title": "Epilóg: Záchrana mentora",
		"description": "Záchrana doktora Van Helsinga a návrat do Budapešti. Prekliate dedičstvo je definitívne zlomené.",
		"puzzles": []
	}
}

func _ready():
	load_game_data()

# Navigácia medzi scénami
func go_to_main_menu():
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func go_to_chapters():
	get_tree().change_scene_to_file("res://scenes/NewChaptersMenu.tscn")

func go_to_chapter(chapter_number: int):
	current_chapter = chapter_number
	last_played_chapter = chapter_number
	last_story_phase = 0  # Reset story phase when starting new chapter
	is_continuing_game = false  # Nová kapitola, nie pokračovanie
	save_game_data()  # Save progress immediately

	# Zastaviť všetky audio efekty z predchádzajúcich kapitol
	if AudioManager:
		AudioManager.stop_all_chapter_audio()

	print("🎬 Spúšťam intro pre kapitolu ", chapter_number)
	# Najprv spustí ChapterIntro, ktoré potom načíta kapitolu
	get_tree().change_scene_to_file("res://scenes/ChapterIntro.tscn")

func go_to_settings():
	get_tree().change_scene_to_file("res://scenes/NewSettingsMenu.tscn")

func go_to_about():
	get_tree().change_scene_to_file("res://scenes/NewAboutGame.tscn")

# Správa progresu
func complete_puzzle(chapter_number: int, puzzle_number: int):
	if not completed_puzzles.has(chapter_number):
		completed_puzzles[chapter_number] = []

	if puzzle_number not in completed_puzzles[chapter_number]:
		completed_puzzles[chapter_number].append(puzzle_number)
		puzzle_completed.emit(chapter_number, puzzle_number)

	# Ak sú dokončené oba hlavolamy, kapitola je hotová
	# Kapitoly 1-6 majú hlavolamy, kapitola 7 (epilóg) sa dokončuje manuálne
	if chapter_number < 7 and completed_puzzles[chapter_number].size() >= 2:
		complete_chapter(chapter_number)

# Špeciálna funkcia pre dokončenie epilógu
func complete_epilogue():
	complete_chapter(7)

func complete_chapter(chapter_number: int):
	if chapter_number not in completed_chapters:
		completed_chapters.append(chapter_number)
		chapter_completed.emit(chapter_number)
	save_game_data()

func is_chapter_unlocked(chapter_number: int) -> bool:
	if chapter_number == 1:
		return true
	# Pre testovanie - odomknúť kapitoly 2, 3, 4, 5, 6 a 7
	if chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6 or chapter_number == 7:
		return true
	return (chapter_number - 1) in completed_chapters

func is_puzzle_completed(chapter_number: int, puzzle_number: int) -> bool:
	if not completed_puzzles.has(chapter_number):
		return false
	return puzzle_number in completed_puzzles[chapter_number]

# Ukladanie a načítavanie
func save_game_data():
	var save_data = {
		"completed_chapters": completed_chapters,
		"completed_puzzles": completed_puzzles,
		"last_played_chapter": last_played_chapter,
		"last_story_phase": last_story_phase,
		"last_dialogue_index": last_dialogue_index,
		"current_game_state": current_game_state,
		"current_puzzle_name": current_puzzle_name,
		"current_dialogue_data": current_dialogue_data,
		"game_settings": game_settings,
		"collected_items": collected_items,
		"used_items": used_items
	}

	var save_file = FileAccess.open("user://savegame.save", FileAccess.WRITE)
	if save_file:
		save_file.store_string(JSON.stringify(save_data))
		save_file.close()

func load_game_data():
	var save_file = FileAccess.open("user://savegame.save", FileAccess.READ)
	if save_file:
		var json_string = save_file.get_as_text()
		save_file.close()

		var json = JSON.new()
		var parse_result = json.parse(json_string)

		if parse_result == OK:
			var save_data = json.data
			var loaded_chapters = save_data.get("completed_chapters", [])
			# Konvertovať Array na Array[int] pre type safety
			completed_chapters.clear()
			for chapter in loaded_chapters:
				if chapter is int:
					completed_chapters.append(chapter)
			completed_puzzles = save_data.get("completed_puzzles", {})
			last_played_chapter = save_data.get("last_played_chapter", 1)
			last_story_phase = save_data.get("last_story_phase", 0)
			last_dialogue_index = save_data.get("last_dialogue_index", 0)
			current_game_state = save_data.get("current_game_state", "story")
			current_puzzle_name = save_data.get("current_puzzle_name", "")
			var loaded_dialogue_data = save_data.get("current_dialogue_data", [])
			# Konvertovať Array pre type safety
			current_dialogue_data.clear()
			for dialogue_item in loaded_dialogue_data:
				if dialogue_item is Dictionary:
					current_dialogue_data.append(dialogue_item)
			game_settings = save_data.get("game_settings", game_settings)

			# Načítať inventory dáta
			var loaded_collected_items = save_data.get("collected_items", [])
			collected_items.clear()
			for item in loaded_collected_items:
				if item is String:
					collected_items.append(item)

			var loaded_used_items = save_data.get("used_items", [])
			used_items.clear()
			for item in loaded_used_items:
				if item is String:
					used_items.append(item)

# Nastavenia
func update_setting(setting_name: String, value):
	game_settings[setting_name] = value
	apply_settings()
	save_game_data()

func apply_settings():
	# Aplikovanie nastavení
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index("Master"),
		linear_to_db(game_settings.master_volume))

# Systém pokračovania hry
func has_save_game() -> bool:
	return FileAccess.file_exists("user://savegame.save")

func get_continue_chapter() -> int:
	if not has_save_game():
		return 1

	# Ak je posledná hraná kapitola dokončená, pokračuj na ďalšiu
	if last_played_chapter in completed_chapters:
		var next_chapter = last_played_chapter + 1
		if next_chapter <= 7:  # Máme 7 kapitol
			return next_chapter
		else:
			return 7  # Zostať na epilógu

	# Inak pokračuj na poslednej hranej kapitole
	return last_played_chapter

func continue_game():
	var continue_chapter = get_continue_chapter()
	# Nastaviť flag že pokračujeme v hre (nie nová hra)
	is_continuing_game = true
	current_chapter = continue_chapter
	last_played_chapter = continue_chapter
	print("🎮 Pokračujem v hre - kapitola: ", continue_chapter, ", stav: ", current_game_state)
	print("🎮 Nastavujem current_chapter a last_played_chapter na: ", continue_chapter)

	# Okamžite zastav main menu hudbu a všetky audio efekty pred načítaním kapitoly
	if AudioManager:
		AudioManager.stop_main_menu_music_immediately()
		AudioManager.stop_all_chapter_audio()

	get_tree().change_scene_to_file("res://scenes/Chapter" + str(continue_chapter) + ".tscn")

func update_story_progress(chapter: int, story_phase: int, dialogue_index: int = 0):
	last_played_chapter = chapter
	last_story_phase = story_phase
	last_dialogue_index = dialogue_index
	save_game_data()

# Nové funkcie pre tracking presného stavu
func set_game_state_story():
	"""Nastaví stav hry na story mode"""
	current_game_state = "story"
	current_puzzle_name = ""
	current_dialogue_data = []
	save_game_data()

func set_game_state_dialogue(dialogue_data: Array, dialogue_index: int = 0):
	"""Nastaví stav hry na dialogue mode s konkrétnymi dátami"""
	current_game_state = "dialogue"
	current_dialogue_data = dialogue_data
	last_dialogue_index = dialogue_index
	current_puzzle_name = ""
	save_game_data()

func set_game_state_puzzle(puzzle_name: String):
	"""Nastaví stav hry na puzzle mode"""
	current_game_state = "puzzle"
	current_puzzle_name = puzzle_name
	current_dialogue_data = []
	save_game_data()

func restore_exact_game_state():
	"""Obnoví presný stav hry po načítaní kapitoly"""
	print("🔄 Obnovujem stav hry: ", current_game_state)

	# Počkať na načítanie scény
	await get_tree().process_frame
	await get_tree().process_frame

	var current_scene = get_tree().current_scene
	if not current_scene:
		print("❌ Chyba: Žiadna aktuálna scéna")
		return

	match current_game_state:
		"dialogue":
			print("💬 Obnovujem dialóg na pozícii: ", last_dialogue_index)
			restore_dialogue_state(current_scene)
		"puzzle":
			print("🧩 Obnovujem puzzle: ", current_puzzle_name)
			restore_puzzle_state(current_scene)
		"story":
			print("📖 Obnovujem story mode")
			restore_story_state(current_scene)

func restore_dialogue_state(scene):
	"""Obnoví dialóg na správnej pozícii"""
	var dialogue_system = scene.get_node("DialogueSystem")
	if dialogue_system and current_dialogue_data.size() > 0:
		# Nastaviť kapitolu pre správne audio
		dialogue_system.set_current_chapter(last_played_chapter)
		dialogue_system.restore_dialogue_position(current_dialogue_data, last_dialogue_index)

func restore_puzzle_state(scene):
	"""Obnoví puzzle stav"""
	# Nájsť a otvoriť správny puzzle
	if current_puzzle_name != "":
		var puzzle_scene = load("res://scenes/" + current_puzzle_name + ".tscn")
		if puzzle_scene:
			var puzzle_instance = puzzle_scene.instantiate()
			scene.add_child(puzzle_instance)

func restore_story_state(scene):
	"""Obnoví story stav na správnej fáze"""
	# Nastaviť správnu story_phase
	if scene.has_method("set_story_phase"):
		scene.set_story_phase(last_story_phase)

# ============================================================================
# INVENTORY SYSTÉM
# ============================================================================

# Zbieranie predmetov
func collect_item(item_id: String):
	"""Pridá predmet do inventory"""
	if item_id not in collected_items:
		collected_items.append(item_id)
		item_collected.emit(item_id)
		save_game_data()
		print("🎒 Získaný predmet: ", item_id)

func has_item(item_id: String) -> bool:
	"""Skontroluje či hráč má predmet"""
	return item_id in collected_items

func use_item(item_id: String):
	"""Označí predmet ako použitý"""
	if item_id not in used_items:
		used_items.append(item_id)
		item_used.emit(item_id)
		save_game_data()
		print("🔧 Použitý predmet: ", item_id)

func is_item_used(item_id: String) -> bool:
	"""Skontroluje či bol predmet použitý"""
	return item_id in used_items

func get_collected_items() -> Array:
	"""Vráti zoznam všetkých získaných predmetov"""
	return collected_items.duplicate()

func get_available_items() -> Array:
	"""Vráti zoznam dostupných (nepoužitých) predmetov"""
	var available: Array = []
	for item in collected_items:
		if item not in used_items:
			available.append(item)
	return available

# Kombinovanie predmetov
func can_combine_items(item1_id: String, item2_id: String) -> bool:
	"""Skontroluje či sa dajú predmety kombinovať"""
	var combinations = get_item_combinations()
	var key1 = item1_id + "+" + item2_id
	var key2 = item2_id + "+" + item1_id
	return combinations.has(key1) or combinations.has(key2)

func combine_items(item1_id: String, item2_id: String) -> String:
	"""Kombinuje dva predmety a vráti výsledok"""
	var combinations = get_item_combinations()
	var key1 = item1_id + "+" + item2_id
	var key2 = item2_id + "+" + item1_id

	if combinations.has(key1):
		var result = combinations[key1]
		collect_item(result)
		return result
	elif combinations.has(key2):
		var result = combinations[key2]
		collect_item(result)
		return result

	return ""

func get_item_combinations() -> Dictionary:
	"""Definície kombinácií predmetov"""
	return {
		# KAPITOLA 4: Elixír kombinovanie
		"recept_na_elixir+strieborny_prasok": "nedokonceny_elixir",
		"nedokonceny_elixir+svata_bylina": "takmer_hotovy_elixir",
		"takmer_hotovy_elixir+svatena_voda": "ochranny_elixir",

		# KAPITOLA 6: Rituálne kombinovanie
		"ritualny_kriz+svatena_voda": "ritual_combo_1",
		"ritual_combo_1+ritualna_sol": "ritual_combo_2",
		"ritual_combo_2+svaty_ohen": "kompletny_ritual",

		# EPILÓG: Liečivý elixír
		"ochranny_elixir+van_helsingov_dennik": "liecivý_elixir"
	}

# Automatické získavanie predmetov podľa kapitoly a fázy
func auto_collect_chapter_items(chapter: int, story_phase: int):
	"""Automaticky pridá predmety podľa kapitoly a fázy"""
	match chapter:
		1:
			match story_phase:
				0:
					collect_item("telegram")
				2:
					collect_item("desifrovana_sprava")
				4:
					collect_item("kocisove_varovanie")
				6:
					collect_item("navigacne_poznamky")
		2:
			match story_phase:
				1:
					collect_item("krvavy_napis")
				3:
					collect_item("riesenie_brany")
				5:
					collect_item("pozehnaný_kriz")
		3:
			match story_phase:
				1:
					collect_item("van_helsingov_dennik")
				3:
					collect_item("desifrovany_dennik")
				5:
					collect_item("isabelle_info")
				7:
					collect_item("mapa_tajneho_kridla")
		4:
			match story_phase:
				1:
					collect_item("basnička")
				3:
					collect_item("recept_na_elixir")
					collect_item("strieborny_prasok")
					collect_item("svata_bylina")
					collect_item("svatena_voda")
				5:
					collect_item("ochranny_elixir")
		5:
			match story_phase:
				1:
					collect_item("rozbita_lampa")
					collect_item("prazdny_revolver")
					collect_item("roztrhane_poznamky")
				3:
					collect_item("posledny_zapis")
				5:
					collect_item("styri_sviečky")
					collect_item("kod_z_tienov")
		6:
			match story_phase:
				1:
					collect_item("portret_maria")
					collect_item("portret_anna")
					collect_item("portret_isabelle")
				3:
					collect_item("ritualny_kriz")
					collect_item("svatena_voda")
					collect_item("ritualna_sol")
					collect_item("svaty_ohen")
				5:
					collect_item("ritualne_pokyny")
		7:
			match story_phase:
				1:
					collect_item("liecivý_elixir")
				3:
					collect_item("isabelle_prach")

# Kontrola požiadaviek pre puzzle
func check_puzzle_requirements(puzzle_name: String) -> bool:
	"""Skontroluje či má hráč potrebné predmety pre puzzle"""
	match puzzle_name:
		"CaesarCipherPuzzle":
			return has_item("telegram")
		"NavigationPuzzle":
			return has_item("navigacne_poznamky")
		"BloodInscriptionPuzzle":
			return has_item("krvavy_napis")
		"ReversedMessagePuzzle":
			return has_item("van_helsingov_dennik")
		"SimpleCalculationPuzzle":
			return has_item("isabelle_info")
		"MemoryTestPuzzle":
			return has_item("ochranny_elixir")
		"VampireArithmeticPuzzle":
			return has_item("basnička")
		"ShadowCodePuzzle":
			return has_item("styri_sviečky")
		"ThreeLeverssPuzzle":
			return has_item("posledny_zapis")
		"ThreeSistersPuzzle":
			return has_item("portret_maria") and has_item("portret_anna") and has_item("portret_isabelle")
		"RitualRhythmPuzzle":
			return has_item("ritualny_kriz") and has_item("svatena_voda") and has_item("ritualna_sol") and has_item("svaty_ohen")
		_:
			return true

# Toggle inventory UI
func toggle_inventory():
	"""Prepne viditeľnosť inventory"""
	inventory_visible = !inventory_visible
	# Signál pre UI aktualizáciu sa pošle cez scénu
