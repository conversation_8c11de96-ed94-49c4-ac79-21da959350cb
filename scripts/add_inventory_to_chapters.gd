@tool
extends EditorScript

# Script pre automatické pridanie inventory do všetkých kapitol

func _run():
	print("🎒 Pridávam inventory do všetkých kapitol...")
	
	var chapters = [
		"res://scenes/Chapter1.tscn",
		"res://scenes/Chapter2.tscn", 
		"res://scenes/Chapter3.tscn",
		"res://scenes/Chapter4.tscn",
		"res://scenes/Chapter5.tscn",
		"res://scenes/Chapter6.tscn",
		"res://scenes/Chapter7.tscn"
	]
	
	for chapter_path in chapters:
		add_inventory_to_chapter(chapter_path)
	
	print("✅ Inventory pridané do všetkých kapitol!")

func add_inventory_to_chapter(chapter_path: String):
	print("📝 Upravujem: ", chapter_path)
	
	# Načítanie scény
	var scene = load(chapter_path)
	if not scene:
		print("❌ Nemôžem načítať scénu: ", chapter_path)
		return
	
	var root = scene.instantiate()
	if not root:
		print("❌ Nemôžem vytvoriť inštanciu scény: ", chapter_path)
		return
	
	# Kontrola či už má inventory
	if root.has_node("MobileInventory"):
		print("ℹ️ Scéna už má inventory: ", chapter_path)
		root.queue_free()
		return
	
	# Pridanie inventory button do VBoxContainer/UIContainer
	var vbox = root.get_node("VBoxContainer")
	if not vbox:
		print("❌ Nenašiel som VBoxContainer v: ", chapter_path)
		root.queue_free()
		return
	
	# Vytvorenie UIContainer ak neexistuje
	var ui_container = vbox.get_node_or_null("UIContainer")
	if not ui_container:
		ui_container = HBoxContainer.new()
		ui_container.name = "UIContainer"
		vbox.add_child(ui_container)
		ui_container.owner = root
		
		# Pridanie spacer
		var spacer = Control.new()
		spacer.name = "UISpacer"
		spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
		ui_container.add_child(spacer)
		spacer.owner = root
	
	# Pridanie inventory button
	var inventory_button = Button.new()
	inventory_button.name = "InventoryButton"
	inventory_button.text = "🎒 INVENTORY"
	inventory_button.custom_minimum_size = Vector2(120, 60)
	ui_container.add_child(inventory_button)
	inventory_button.owner = root
	
	# Pridanie MobileInventory scény
	var mobile_inventory_scene = load("res://scenes/MobileInventory.tscn")
	if mobile_inventory_scene:
		var mobile_inventory = mobile_inventory_scene.instantiate()
		mobile_inventory.name = "MobileInventory"
		mobile_inventory.visible = false
		root.add_child(mobile_inventory)
		mobile_inventory.owner = root
	
	# Uloženie scény
	var packed_scene = PackedScene.new()
	packed_scene.pack(root)
	
	var result = ResourceSaver.save(packed_scene, chapter_path)
	if result == OK:
		print("✅ Úspešne upravené: ", chapter_path)
	else:
		print("❌ Chyba pri ukladaní: ", chapter_path)
	
	root.queue_free()

# Funkcia pre manuálne spustenie
func add_inventory_manually():
	_run()
