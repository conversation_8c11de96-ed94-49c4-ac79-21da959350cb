extends Control
class_name BloodInscriptionPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var inscription_label: Label = $PuzzlePanel/VBoxContainer/InscriptionLabel
@onready var letters_container: GridContainer = $PuzzlePanel/VBoxContainer/LettersCenterContainer/LettersContainer
@onready var sequence_label: Label = $PuzzlePanel/VBoxContainer/SequenceLabel
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array = ["T", "R", "M", "S", "O"]
var current_sequence: Array = []
var letter_buttons: Array = []
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()

	print("🔍 BloodInscriptionPuzzle: _ready() called")
	print("🔍 BloodInscriptionPuzzle: letters_container = ", letters_container)

	# Pripojenie signálov
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)

	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Krvavý nápis na bráne"
	
	if description_label:
		description_label.text = "[center]Na bráne je krvou napísaný text.[/center]\n\nRozlúštite správne poradie písmen:"
	
	if inscription_label:
		inscription_label.text = "MOJE MENO JE SMRŤ, ALE POZADU SOM ŽIVOT"
		inscription_label.add_theme_font_size_override("font_size", 18)
		inscription_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		inscription_label.add_theme_color_override("font_color", Color(0.8, 0.2, 0.2))  # Krvavá červená
	
	update_sequence_display()

func create_letter_buttons():
	print("🔍 BloodInscriptionPuzzle: Creating letter buttons...")
	if not letters_container:
		print("❌ BloodInscriptionPuzzle: letters_container is null!")
		return

	print("✅ BloodInscriptionPuzzle: letters_container found: ", letters_container)

	# Vyčistiť existujúce tlačidlá
	for child in letters_container.get_children():
		child.queue_free()

	letter_buttons.clear()

	# Nastavenie grid kontajnera
	letters_container.columns = 5

	# Nastavenie parent kontajnera pre lepšiu viditeľnosť
	var center_container = letters_container.get_parent()
	if center_container:
		center_container.custom_minimum_size = Vector2(400, 100)
		print("✅ BloodInscriptionPuzzle: Set center_container minimum size")

	# Vytvorenie tlačidiel pre písmená T, R, M, S, O v náhodnom poradí
	var letters = ["T", "R", "M", "S", "O"]
	letters.shuffle()

	print("🔤 BloodInscriptionPuzzle: Creating buttons for letters: ", letters)

	for letter in letters:
		var button = Button.new()
		button.text = letter
		button.custom_minimum_size = Vector2(80, 80)
		button.size = Vector2(80, 80)  # Explicitné nastavenie veľkosti
		button.add_theme_font_size_override("font_size", 24)

		# Explicitné nastavenie farieb pre lepšiu viditeľnosť
		button.add_theme_color_override("font_color", Color.WHITE)
		button.add_theme_color_override("font_hover_color", Color.YELLOW)
		button.add_theme_color_override("font_pressed_color", Color.RED)

		# Nastavenie pozadia tlačidla
		var style_normal = StyleBoxFlat.new()
		style_normal.bg_color = Color(0.3, 0.3, 0.3, 0.8)
		style_normal.border_width_left = 2
		style_normal.border_width_right = 2
		style_normal.border_width_top = 2
		style_normal.border_width_bottom = 2
		style_normal.border_color = Color.WHITE
		style_normal.corner_radius_top_left = 5
		style_normal.corner_radius_top_right = 5
		style_normal.corner_radius_bottom_left = 5
		style_normal.corner_radius_bottom_right = 5

		var style_hover = StyleBoxFlat.new()
		style_hover.bg_color = Color(0.5, 0.5, 0.5, 0.9)
		style_hover.border_width_left = 2
		style_hover.border_width_right = 2
		style_hover.border_width_top = 2
		style_hover.border_width_bottom = 2
		style_hover.border_color = Color.YELLOW
		style_hover.corner_radius_top_left = 5
		style_hover.corner_radius_top_right = 5
		style_hover.corner_radius_bottom_left = 5
		style_hover.corner_radius_bottom_right = 5

		button.add_theme_stylebox_override("normal", style_normal)
		button.add_theme_stylebox_override("hover", style_hover)
		button.add_theme_stylebox_override("pressed", style_hover)

		# Pripojenie signálu
		button.pressed.connect(_on_letter_pressed.bind(letter))

		letters_container.add_child(button)
		letter_buttons.append(button)
		print("✅ BloodInscriptionPuzzle: Created button for letter: ", letter)

	print("🎯 BloodInscriptionPuzzle: Total buttons created: ", letter_buttons.size())

func show_puzzle():
	print("🎮 BloodInscriptionPuzzle: Showing puzzle...")
	show()

	# Vytvorenie tlačidiel pri zobrazení puzzle
	create_letter_buttons()

	print("🔤 BloodInscriptionPuzzle: Letter buttons count: ", letter_buttons.size())
	if letter_buttons.size() > 0:
		letter_buttons[0].grab_focus()
		print("✅ BloodInscriptionPuzzle: Focus set to first button")
	else:
		print("❌ BloodInscriptionPuzzle: No letter buttons available!")

func _on_letter_pressed(letter: String):
	current_sequence.append(letter)
	update_sequence_display()
	
	# Kontrola správnosti
	if current_sequence.size() <= correct_sequence.size():
		if current_sequence[current_sequence.size() - 1] != correct_sequence[current_sequence.size() - 1]:
			# Nesprávne písmeno
			AudioManager.play_puzzle_error_sound()
			show_error_feedback()
			var timer = get_tree().create_timer(1.0)
			timer.timeout.connect(reset_sequence)
		elif current_sequence.size() == correct_sequence.size():
			# Hlavolam vyriešený
			AudioManager.play_puzzle_success_sound()
			puzzle_solved.emit()
			hide()

func update_sequence_display():
	if sequence_label:
		var display_text = "Vaša sekvencia: "
		for i in range(current_sequence.size()):
			display_text += current_sequence[i]
			if i < current_sequence.size() - 1:
				display_text += " → "
		
		if current_sequence.size() < correct_sequence.size():
			display_text += " → ?"
		
		sequence_label.text = display_text

func show_error_feedback():
	# Červené zablikanie tlačidiel
	for button in letter_buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	for button in letter_buttons:
		if button:
			tween.parallel().tween_property(button, "modulate", Color.WHITE, 0.5)

func reset_sequence():
	current_sequence.clear()
	update_sequence_display()

func _on_hint_pressed():
	print("🔍 BloodInscriptionPuzzle: Hint button pressed!")
	hint_level += 1

	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_simple_hint_dialog(hint_text)
	else:
		show_simple_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Čítaj pozorne - niekedy je odpoveď v opaku."
		2:
			return "Smrť pozadu... čo dostaneš, keď otočíš slovo?"
		3:
			return "SMRŤ odzadu je TRMS, ale nezabudni na piatu literu z 'SOM' → TRMSO"
		_:
			return "Už ste použili všetky nápovedy!"

func show_simple_hint_dialog(hint_text: String):
	# Vytvorenie responzívneho hint dialógu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	# Responzívna veľkosť - max 80% šírky obrazovky, min 300px
	var screen_size = get_viewport().get_visible_rect().size
	var panel_width = min(max(300, screen_size.x * 0.8), 500)
	var panel_height = min(max(150, screen_size.y * 0.4), 300)
	hint_panel.size = Vector2(panel_width, panel_height)

	# Manuálne centrovanie
	hint_panel.position = Vector2(
		(screen_size.x - panel_width) / 2,
		(screen_size.y - panel_height) / 2
	)
	hint_overlay.add_child(hint_panel)

	# VBoxContainer pre obsah
	var content_vbox = VBoxContainer.new()
	content_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(content_vbox)

	# Margin container
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_left", 35)
	margin_container.add_theme_constant_override("margin_right", 35)
	margin_container.add_theme_constant_override("margin_top", 45)
	margin_container.add_theme_constant_override("margin_bottom", 35)
	content_vbox.add_child(margin_container)

	var inner_vbox = VBoxContainer.new()
	inner_vbox.add_theme_constant_override("separation", 10)
	margin_container.add_child(inner_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	inner_vbox.add_child(title_label)

	# Text nápovedy s automatickým zalamovaním
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	hint_label.custom_minimum_size = Vector2(0, 60)
	inner_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	inner_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func _on_reset_pressed():
	reset_sequence()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()



func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
