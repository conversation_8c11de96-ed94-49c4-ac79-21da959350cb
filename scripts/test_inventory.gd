@tool
extends EditorScript

# Test script pre inventory systém

func _run():
	print("🧪 Testovanie inventory systému...")
	
	# Test 1: Základné funkcie GameManager
	test_basic_functions()
	
	# Test 2: Kombinovanie predmetov
	test_item_combinations()
	
	# Test 3: <PERSON><PERSON><PERSON> prid<PERSON>vani<PERSON> predmetov
	test_auto_collection()
	
	print("✅ Všetky testy dokončené!")

func test_basic_functions():
	print("\n📦 Test 1: Základné funkcie")
	
	# Simulácia GameManager funkcií
	var collected_items: Array = []
	var used_items: Array = []
	
	# Test pridania predmetu
	var item_id = "telegram"
	if item_id not in collected_items:
		collected_items.append(item_id)
		print("✅ Pridaný predmet: ", item_id)
	
	# Test kontroly predmetu
	var has_item = item_id in collected_items
	print("✅ Má predmet '", item_id, "': ", has_item)
	
	# Test použitia predmetu
	if item_id not in used_items:
		used_items.append(item_id)
		print("✅ Použitý predmet: ", item_id)
	
	# Test dostupných predmetov
	var available: Array = []
	for item in collected_items:
		if item not in used_items:
			available.append(item)
	print("✅ Dostupné predmety: ", available)

func test_item_combinations():
	print("\n🔧 Test 2: Kombinovanie predmetov")
	
	var combinations = {
		"recept_na_elixir+strieborny_prasok": "nedokonceny_elixir",
		"nedokonceny_elixir+svata_bylina": "takmer_hotovy_elixir", 
		"takmer_hotovy_elixir+svatena_voda": "ochranny_elixir"
	}
	
	# Test kombinácie
	var item1 = "recept_na_elixir"
	var item2 = "strieborny_prasok"
	var key1 = item1 + "+" + item2
	var key2 = item2 + "+" + item1
	
	if combinations.has(key1):
		var result = combinations[key1]
		print("✅ Kombinácia ", item1, " + ", item2, " = ", result)
	elif combinations.has(key2):
		var result = combinations[key2]
		print("✅ Kombinácia ", item2, " + ", item1, " = ", result)
	else:
		print("❌ Kombinácia sa nedá vykonať")

func test_auto_collection():
	print("\n🎯 Test 3: Automatické pridávanie predmetov")
	
	# Simulácia auto_collect_chapter_items
	var chapter = 1
	var story_phase = 0
	
	var expected_items = get_expected_items_for_phase(chapter, story_phase)
	print("✅ Pre kapitolu ", chapter, ", fázu ", story_phase, " očakávané predmety: ", expected_items)

func get_expected_items_for_phase(chapter: int, story_phase: int) -> Array:
	var items: Array = []
	
	match chapter:
		1:
			match story_phase:
				0:
					items.append("telegram")
				2:
					items.append("desifrovana_sprava")
				4:
					items.append("kocisove_varovanie")
				6:
					items.append("navigacne_poznamky")
		2:
			match story_phase:
				1:
					items.append("krvavy_napis")
				3:
					items.append("riesenie_brany")
				5:
					items.append("pozehnaný_kriz")
	
	return items

# Test načítania JSON súboru
func test_json_loading():
	print("\n📄 Test 4: Načítanie JSON súboru")
	
	var file_path = "res://data/item_prototypes.json"
	var file = FileAccess.open(file_path, FileAccess.READ)
	
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			var data = json.data
			print("✅ JSON načítané úspešne. Počet predmetov: ", data.keys().size())
			
			# Test konkrétneho predmetu
			if data.has("telegram"):
				var telegram = data["telegram"]
				print("✅ Telegram: ", telegram.name, " - ", telegram.description)
			else:
				print("❌ Telegram sa nenašiel v JSON")
		else:
			print("❌ Chyba pri parsovaní JSON")
	else:
		print("❌ Nemôžem otvoriť JSON súbor: ", file_path)

# Spustenie všetkých testov
func run_all_tests():
	test_basic_functions()
	test_item_combinations()
	test_auto_collection()
	test_json_loading()
