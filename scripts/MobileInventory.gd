extends Control

# Mobilné inventory UI optimalizované pre touch ovládanie
signal item_selected(item_id: String)
signal item_used(item_id: String)
signal items_combined(item1_id: String, item2_id: String)

@onready var item_grid = $InventoryPanel/VBoxContainer/ScrollContainer/ItemGrid
@onready var use_button = $InventoryPanel/VBoxContainer/ActionContainer/UseButton
@onready var combine_button = $InventoryPanel/VBoxContainer/ActionContainer/CombineButton
@onready var info_button = $InventoryPanel/VBoxContainer/ActionContainer/InfoButton
@onready var item_info_panel = $ItemInfoPanel
@onready var info_title = $ItemInfoPanel/InfoVBox/ItemName
@onready var info_description = $ItemInfoPanel/InfoVBox/ItemDescription
@onready var info_usage = $ItemInfoPanel/InfoVBox/ItemUsage
@onready var info_icon = $ItemInfoPanel/InfoVBox/ItemIcon

var item_prototypes: Dictionary = {}
var selected_items: Array = []
var item_buttons: Dictionary = {}
var combination_system: ItemCombinationSystem
var mobile_optimizations: MobileOptimizations

func _ready():
	# Vytvorenie combination systému
	combination_system = ItemCombinationSystem.new()
	add_child(combination_system)

	# Vytvorenie mobile optimizations
	mobile_optimizations = MobileOptimizations.new()
	add_child(mobile_optimizations)

	load_item_prototypes()
	refresh_inventory()

	# Pripojenie signálov z GameManager
	if GameManager:
		GameManager.item_collected.connect(_on_item_collected)
		GameManager.item_used.connect(_on_item_used)

	# Pripojenie signálov z combination systému
	if combination_system:
		combination_system.combination_successful.connect(_on_combination_successful)
		combination_system.combination_failed.connect(_on_combination_failed)

	# Pripojenie signálov z mobile optimizations
	if mobile_optimizations:
		mobile_optimizations.screen_size_changed.connect(_on_screen_size_changed)
		mobile_optimizations.orientation_changed.connect(_on_orientation_changed)

	# Optimalizácia pre mobilné zariadenia
	optimize_for_mobile()

func load_item_prototypes():
	"""Načíta definície predmetov z JSON súboru"""
	var file = FileAccess.open("res://data/item_prototypes.json", FileAccess.READ)
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			item_prototypes = json.data
			print("📦 Načítané item prototypes: ", item_prototypes.keys().size())
		else:
			print("❌ Chyba pri načítaní item prototypes")

func refresh_inventory():
	"""Obnoví zobrazenie inventory"""
	clear_item_grid()
	
	var available_items = GameManager.get_available_items()
	
	for item_id in available_items:
		create_item_button(item_id)
	
	update_action_buttons()

func clear_item_grid():
	"""Vymaže všetky item buttony"""
	for child in item_grid.get_children():
		child.queue_free()
	item_buttons.clear()

func create_item_button(item_id: String):
	"""Vytvorí button pre predmet"""
	if not item_prototypes.has(item_id):
		print("❌ Neznámy predmet: ", item_id)
		return
	
	var item_data = item_prototypes[item_id]
	var button = Button.new()
	
	# Nastavenie veľkosti pre mobilné zariadenia
	var device_type = mobile_optimizations.get_device_type() if mobile_optimizations else "desktop"
	match device_type:
		"phone":
			button.custom_minimum_size = Vector2(100, 100)
		"tablet":
			button.custom_minimum_size = Vector2(120, 120)
		_:
			button.custom_minimum_size = Vector2(140, 140)

	button.text = item_data.name
	button.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	
	# Načítanie ikony ak existuje
	if item_data.has("image") and ResourceLoader.exists(item_data.image):
		var texture = load(item_data.image)
		button.icon = texture
		button.icon_alignment = HORIZONTAL_ALIGNMENT_CENTER
		button.vertical_icon_alignment = VERTICAL_ALIGNMENT_TOP
	
	# Styling pre mobilné zariadenia
	button.add_theme_font_size_override("font_size", 14)
	
	# Pripojenie signálu
	button.pressed.connect(_on_item_button_pressed.bind(item_id))

	# Mobilné optimalizácie
	if mobile_optimizations:
		mobile_optimizations.add_touch_feedback(button)

	# Pridanie do gridu
	item_grid.add_child(button)
	item_buttons[item_id] = button

func _on_item_button_pressed(item_id: String):
	"""Spracuje kliknutie na predmet"""
	if item_id in selected_items:
		# Odznačiť predmet
		selected_items.erase(item_id)
		item_buttons[item_id].modulate = Color.WHITE
	else:
		# Označiť predmet (max 2 pre kombinovanie)
		if selected_items.size() < 2:
			selected_items.append(item_id)
			item_buttons[item_id].modulate = Color.YELLOW
	
	update_action_buttons()
	item_selected.emit(item_id)

func update_action_buttons():
	"""Aktualizuje stav action buttonov"""
	var selected_count = selected_items.size()
	
	# Use button - aktívny ak je vybraný 1 predmet
	use_button.disabled = selected_count != 1
	
	# Combine button - aktívny ak sú vybrané 2 predmety a dajú sa kombinovať
	if selected_count == 2 and combination_system:
		combine_button.disabled = not combination_system.can_combine_items(selected_items[0], selected_items[1])
	else:
		combine_button.disabled = true
	
	# Info button - aktívny ak je vybraný 1 predmet
	info_button.disabled = selected_count != 1

func _on_use_button_pressed():
	"""Použije vybraný predmet"""
	if selected_items.size() == 1:
		var item_id = selected_items[0]

		# Kontrola či predmet odomkne puzzle
		if combination_system:
			var puzzle_name = combination_system.unlock_puzzle_with_item(item_id)
			if puzzle_name != "":
				print("🧩 Odomknutý puzzle: ", puzzle_name)
				# Signál pre kapitolu aby spustila puzzle
				item_used.emit(item_id)
			else:
				# Normálne použitie predmetu
				item_used.emit(item_id)
				GameManager.use_item(item_id)
		else:
			item_used.emit(item_id)
			GameManager.use_item(item_id)

		clear_selection()
		refresh_inventory()

func _on_combine_button_pressed():
	"""Kombinuje vybrané predmety"""
	if selected_items.size() == 2 and combination_system:
		var item1 = selected_items[0]
		var item2 = selected_items[1]

		var result = combination_system.combine_items(item1, item2)
		if result.success:
			# Pridanie výsledného predmetu do GameManager
			GameManager.collect_item(result.result_item)
			items_combined.emit(item1, item2)
			show_combination_result(result.result_item, result.message)
		else:
			show_combination_failed(result.message)

		clear_selection()
		refresh_inventory()

func _on_info_button_pressed():
	"""Zobrazí informácie o predmete"""
	if selected_items.size() == 1:
		show_item_info(selected_items[0])

func show_item_info(item_id: String):
	"""Zobrazí detailné informácie o predmete"""
	if not item_prototypes.has(item_id):
		return
	
	var item_data = item_prototypes[item_id]
	
	info_title.text = item_data.name
	info_description.text = item_data.description
	info_usage.text = "Použitie: " + item_data.get("usage", "Neznáme")
	
	# Načítanie ikony
	if item_data.has("image") and ResourceLoader.exists(item_data.image):
		var texture = load(item_data.image)
		info_icon.texture = texture
	else:
		info_icon.texture = null
	
	item_info_panel.visible = true

func show_combination_result(result_item_id: String, message: String = ""):
	"""Zobrazí výsledok kombinácie"""
	if item_prototypes.has(result_item_id):
		var item_data = item_prototypes[result_item_id]
		var display_message = message if message != "" else "Vytvorený predmet: " + item_data.name
		print("✨ ", display_message)
		# TODO: Pridať toast notifikáciu alebo popup

func show_combination_failed(message: String):
	"""Zobrazí neúspešnú kombináciu"""
	print("❌ ", message)
	# TODO: Pridať error popup

func clear_selection():
	"""Vymaže výber predmetov"""
	for item_id in selected_items:
		if item_buttons.has(item_id):
			item_buttons[item_id].modulate = Color.WHITE
	selected_items.clear()
	update_action_buttons()

func _on_close_button_pressed():
	"""Zatvorí inventory"""
	visible = false
	GameManager.inventory_visible = false

func _on_close_info_button_pressed():
	"""Zatvorí info panel"""
	item_info_panel.visible = false

func _on_item_collected(item_id: String):
	"""Reaguje na získanie nového predmetu"""
	refresh_inventory()

func _on_item_used(item_id: String):
	"""Reaguje na použitie predmetu"""
	refresh_inventory()

func _on_combination_successful(result_item: String):
	"""Reaguje na úspešnú kombináciu"""
	print("🎉 Kombinácia úspešná: ", result_item)

func _on_combination_failed(item1: String, item2: String):
	"""Reaguje na neúspešnú kombináciu"""
	print("💥 Kombinácia zlyhala: ", item1, " + ", item2)

# Touch gesture support pre mobilné zariadenia
func _gui_input(event):
	if event is InputEventScreenTouch:
		if event.pressed:
			# Môžeme pridať gesture rozpoznávanie
			pass

# Automatické škálovanie pre rôzne veľkosti obrazoviek
func _notification(what):
	if what == NOTIFICATION_RESIZED:
		adjust_for_screen_size()

func adjust_for_screen_size():
	"""Prispôsobí UI veľkosti obrazovky"""
	var screen_size = get_viewport().size
	
	# Pre malé obrazovky zmenšiť grid
	if screen_size.x < 600:
		item_grid.columns = 2
		# Zmenšiť buttony
		for button in item_buttons.values():
			button.custom_minimum_size = Vector2(100, 100)
	else:
		item_grid.columns = 3
		for button in item_buttons.values():
			button.custom_minimum_size = Vector2(120, 120)

# Funkcie pre integráciu s puzzle systémom
func get_items_for_puzzle(puzzle_name: String) -> Array:
	"""Vráti predmety potrebné pre konkrétny puzzle"""
	var required_items: Array = []
	
	match puzzle_name:
		"CaesarCipherPuzzle":
			if GameManager.has_item("telegram"):
				required_items.append("telegram")
		"NavigationPuzzle":
			if GameManager.has_item("navigacne_poznamky"):
				required_items.append("navigacne_poznamky")
		"VampireArithmeticPuzzle":
			if GameManager.has_item("basnička"):
				required_items.append("basnička")
		# Pridať ďalšie puzzle podľa potreby
	
	return required_items

func highlight_required_items(required_items: Array):
	"""Zvýrazní potrebné predmety pre puzzle"""
	for item_id in required_items:
		if item_buttons.has(item_id):
			item_buttons[item_id].modulate = Color.GREEN

# ============================================================================
# MOBILNÉ OPTIMALIZÁCIE
# ============================================================================

func optimize_for_mobile():
	"""Optimalizuje inventory pre mobilné zariadenia"""
	if mobile_optimizations:
		mobile_optimizations.optimize_inventory_for_mobile(self)

		# Optimalizácia action buttonov
		mobile_optimizations.add_touch_feedback(use_button)
		mobile_optimizations.add_touch_feedback(combine_button)
		mobile_optimizations.add_touch_feedback(info_button)

func _on_screen_size_changed(new_size: Vector2):
	"""Reaguje na zmenu veľkosti obrazovky"""
	print("📱 Inventory: Screen size changed to ", new_size)
	adjust_for_screen_size()

func _on_orientation_changed(is_portrait: bool):
	"""Reaguje na zmenu orientácie"""
	print("📱 Inventory: Orientation changed to ", "portrait" if is_portrait else "landscape")
	adjust_for_screen_size()

func adjust_for_screen_size():
	"""Prispôsobí UI veľkosti obrazovky"""
	if not mobile_optimizations:
		return

	var device_type = mobile_optimizations.get_device_type()

	# Prispôsobenie počtu stĺpcov v grid
	match device_type:
		"phone":
			item_grid.columns = 2
			# Zmenšenie item buttonov pre telefóny
			for button in item_buttons.values():
				button.custom_minimum_size = Vector2(100, 100)
		"tablet":
			item_grid.columns = 3
			for button in item_buttons.values():
				button.custom_minimum_size = Vector2(120, 120)
		_:
			item_grid.columns = 4
			for button in item_buttons.values():
				button.custom_minimum_size = Vector2(140, 140)
