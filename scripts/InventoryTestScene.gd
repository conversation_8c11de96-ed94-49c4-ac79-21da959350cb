extends Control

# Test scéna pre inventory systém

@onready var mobile_inventory = $MobileInventory
@onready var info_label = $VBoxContainer/InfoLabel

var combination_system: ItemCombinationSystem

func _ready():
	print("🧪 Inventory test scéna načítaná")
	
	# Vytvorenie combination systému
	combination_system = ItemCombinationSystem.new()
	add_child(combination_system)
	
	# Pripojenie signálov
	if combination_system:
		combination_system.combination_successful.connect(_on_combination_successful)
		combination_system.combination_failed.connect(_on_combination_failed)

func _on_add_items_button_pressed():
	"""Pridá testové predmety do inventory"""
	print("📦 Pridávam testové predmety...")
	
	# Kapitola 1 predmety
	GameManager.collect_item("telegram")
	GameManager.collect_item("navigacne_poznamky")
	
	# Kapitola 4 predmety pre testovanie kombinácií
	GameManager.collect_item("recept_na_elixir")
	GameManager.collect_item("strieborny_prasok")
	GameManager.collect_item("svata_bylina")
	GameManager.collect_item("svatena_voda")
	
	# Kapitola 6 predmety
	GameManager.collect_item("ritualny_kriz")
	GameManager.collect_item("ritualna_sol")
	GameManager.collect_item("svaty_ohen")
	
	update_info_display()
	print("✅ Testové predmety pridané!")

func _on_show_inventory_button_pressed():
	"""Zobrazí inventory"""
	if mobile_inventory:
		mobile_inventory.visible = !mobile_inventory.visible
		print("👁️ Inventory ", "zobrazené" if mobile_inventory.visible else "skryté")

func _on_test_combination_button_pressed():
	"""Testuje automatické kombinovanie"""
	print("🔧 Testovanie kombinácií...")
	
	# Test 1: Recept + Strieborný prášok
	if GameManager.has_item("recept_na_elixir") and GameManager.has_item("strieborny_prasok"):
		var result = combination_system.combine_items("recept_na_elixir", "strieborny_prasok")
		if result.success:
			GameManager.collect_item(result.result_item)
			print("✅ Test 1 úspešný: ", result.message)
		else:
			print("❌ Test 1 zlyhal: ", result.message)
	
	# Test 2: Nedokončený elixír + Svätá bylina
	if GameManager.has_item("nedokonceny_elixir") and GameManager.has_item("svata_bylina"):
		var result = combination_system.combine_items("nedokonceny_elixir", "svata_bylina")
		if result.success:
			GameManager.collect_item(result.result_item)
			print("✅ Test 2 úspešný: ", result.message)
		else:
			print("❌ Test 2 zlyhal: ", result.message)
	
	# Test 3: Takmer hotový elixír + Svätená voda
	if GameManager.has_item("takmer_hotovy_elixir") and GameManager.has_item("svatena_voda"):
		var result = combination_system.combine_items("takmer_hotovy_elixir", "svatena_voda")
		if result.success:
			GameManager.collect_item(result.result_item)
			print("✅ Test 3 úspešný: ", result.message)
		else:
			print("❌ Test 3 zlyhal: ", result.message)
	
	update_info_display()

func _on_clear_inventory_button_pressed():
	"""Vymaže inventory"""
	print("🗑️ Mažem inventory...")
	GameManager.collected_items.clear()
	GameManager.used_items.clear()
	GameManager.save_game_data()
	update_info_display()
	print("✅ Inventory vymazané!")

func _on_back_button_pressed():
	"""Návrat do hlavného menu"""
	GameManager.go_to_main_menu()

func update_info_display():
	"""Aktualizuje zobrazenie informácií"""
	var collected_items = GameManager.get_collected_items()
	var available_items = GameManager.get_available_items()
	var used_items = GameManager.used_items
	
	var info_text = "[center]STAV INVENTORY[/center]\n\n"
	info_text += "[b]Získané predmety (" + str(collected_items.size()) + "):[/b]\n"
	
	for item in collected_items:
		var status = " ✓" if item in used_items else " 📦"
		info_text += "• " + item + status + "\n"
	
	info_text += "\n[b]Dostupné predmety:[/b] " + str(available_items.size())
	info_text += "\n[b]Použité predmety:[/b] " + str(used_items.size())
	
	# Dostupné kombinácie
	if combination_system:
		var combinations = combination_system.get_available_combinations()
		if combinations.size() > 0:
			info_text += "\n\n[b]Dostupné kombinácie:[/b]\n"
			for combo in combinations:
				info_text += "• " + combo.item1 + " + " + combo.item2 + " = " + combo.result + "\n"
	
	if info_label:
		info_label.text = info_text

func _on_combination_successful(result_item: String):
	"""Callback pre úspešnú kombináciu"""
	print("🎉 Kombinácia úspešná: ", result_item)
	update_info_display()

func _on_combination_failed(item1: String, item2: String):
	"""Callback pre neúspešnú kombináciu"""
	print("💥 Kombinácia zlyhala: ", item1, " + ", item2)

# Test puzzle requirements
func test_puzzle_requirements():
	"""Testuje požiadavky pre puzzle"""
	print("\n🧩 Testovanie puzzle požiadaviek...")
	
	var puzzles = [
		"CaesarCipherPuzzle",
		"NavigationPuzzle", 
		"BloodInscriptionPuzzle",
		"VampireArithmeticPuzzle",
		"MemoryTestPuzzle",
		"RitualRhythmPuzzle"
	]
	
	for puzzle_name in puzzles:
		if combination_system:
			var requirements = combination_system.check_puzzle_requirements(puzzle_name)
			var status = "✅" if requirements.can_start else "❌"
			print(status, " ", puzzle_name, " - Chýbajú: ", requirements.missing_items)

# Test automatických návrhov
func test_suggestions():
	"""Testuje automatické návrhy kombinácií"""
	print("\n💡 Testovanie návrhov...")
	
	if combination_system:
		var suggestion = combination_system.suggest_next_combination()
		if suggestion.suggested:
			print("💡 Navrhovaná kombinácia: ", suggestion.item1, " + ", suggestion.item2)
			print("   Dôvod: ", suggestion.reason)
		else:
			print("💡 Žiadne návrhy dostupné")

# Spustenie všetkých testov
func run_all_tests():
	"""Spustí všetky testy"""
	print("\n🧪 Spúšťam všetky testy...")
	_on_add_items_button_pressed()
	await get_tree().process_frame
	_on_test_combination_button_pressed()
	await get_tree().process_frame
	test_puzzle_requirements()
	test_suggestions()
	print("✅ Všetky testy dokončené!")

func _input(event):
	if event.is_action_pressed("ui_accept"):
		# Spustenie testov klávesou Enter
		run_all_tests()
