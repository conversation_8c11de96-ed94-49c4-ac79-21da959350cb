extends Node
class_name MobileOptimizations

# Optimalizácie pre mobilné zariadenia

signal screen_size_changed(new_size: Vector2)
signal orientation_changed(is_portrait: bool)

var current_screen_size: Vector2
var is_portrait_mode: bool = true
var touch_sensitivity: float = 1.0

# Mobilné UI konštanty
const MIN_TOUCH_TARGET_SIZE = Vector2(60, 60)
const MOBILE_FONT_SIZE_MULTIPLIER = 1.2
const TABLET_BREAKPOINT = 800
const PHONE_BREAKPOINT = 600

func _ready():
	# Detekcia počiatočnej veľkosti obrazovky
	current_screen_size = get_viewport().size
	is_portrait_mode = current_screen_size.y > current_screen_size.x
	
	# Pripojenie na zmeny veľkosti
	get_viewport().size_changed.connect(_on_viewport_size_changed)
	
	print("📱 Mobile optimizations initialized")
	print("   Screen size: ", current_screen_size)
	print("   Portrait mode: ", is_portrait_mode)

func _on_viewport_size_changed():
	"""Reaguje na zmenu veľkosti obrazovky"""
	var new_size = get_viewport().size
	var new_portrait = new_size.y > new_size.x
	
	if new_size != current_screen_size:
		current_screen_size = new_size
		screen_size_changed.emit(new_size)
		print("📱 Screen size changed: ", new_size)
	
	if new_portrait != is_portrait_mode:
		is_portrait_mode = new_portrait
		orientation_changed.emit(is_portrait_mode)
		print("📱 Orientation changed: ", "Portrait" if is_portrait_mode else "Landscape")

func get_device_type() -> String:
	"""Určí typ zariadenia na základe veľkosti obrazovky"""
	var width = current_screen_size.x
	
	if width < PHONE_BREAKPOINT:
		return "phone"
	elif width < TABLET_BREAKPOINT:
		return "tablet"
	else:
		return "desktop"

func get_optimal_ui_scale() -> float:
	"""Vráti optimálnu škálu UI pre aktuálne zariadenie"""
	var device_type = get_device_type()
	
	match device_type:
		"phone":
			return 1.5
		"tablet":
			return 1.2
		_:
			return 1.0

func optimize_control_for_mobile(control: Control):
	"""Optimalizuje Control node pre mobilné zariadenia"""
	if not control:
		return
	
	var device_type = get_device_type()
	var scale = get_optimal_ui_scale()
	
	# Minimálna veľkosť pre touch targets
	if control is Button:
		var button = control as Button
		var min_size = MIN_TOUCH_TARGET_SIZE * scale
		
		if button.custom_minimum_size.x < min_size.x:
			button.custom_minimum_size.x = min_size.x
		if button.custom_minimum_size.y < min_size.y:
			button.custom_minimum_size.y = min_size.y
		
		# Väčší font pre mobilné zariadenia
		if device_type in ["phone", "tablet"]:
			var font_size = button.get_theme_font_size("font_size")
			if font_size > 0:
				button.add_theme_font_size_override("font_size", int(font_size * MOBILE_FONT_SIZE_MULTIPLIER))
	
	# Optimalizácia pre GridContainer (inventory grid)
	if control is GridContainer:
		var grid = control as GridContainer
		match device_type:
			"phone":
				grid.columns = 2  # Menej stĺpcov pre telefóny
			"tablet":
				grid.columns = 3
			_:
				grid.columns = 4

func optimize_inventory_for_mobile(inventory: Control):
	"""Špecifické optimalizácie pre inventory"""
	if not inventory:
		return
	
	var device_type = get_device_type()
	
	# Nájdenie item grid
	var item_grid = inventory.find_child("ItemGrid", true, false)
	if item_grid and item_grid is GridContainer:
		optimize_control_for_mobile(item_grid)
	
	# Optimalizácia action buttonov
	var action_container = inventory.find_child("ActionContainer", true, false)
	if action_container:
		for child in action_container.get_children():
			if child is Button:
				optimize_control_for_mobile(child)
	
	# Prispôsobenie veľkosti inventory panelu
	var inventory_panel = inventory.find_child("InventoryPanel", true, false)
	if inventory_panel and inventory_panel is Control:
		match device_type:
			"phone":
				# Menší panel pre telefóny
				inventory_panel.custom_minimum_size = Vector2(280, 400)
			"tablet":
				inventory_panel.custom_minimum_size = Vector2(400, 500)
			_:
				inventory_panel.custom_minimum_size = Vector2(600, 600)

func add_touch_feedback(button: Button):
	"""Pridá touch feedback pre button"""
	if not button:
		return
	
	# Vizuálny feedback pri stlačení
	button.button_down.connect(_on_button_touch_down.bind(button))
	button.button_up.connect(_on_button_touch_up.bind(button))

func _on_button_touch_down(button: Button):
	"""Touch down feedback"""
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color(0.8, 0.8, 0.8), 0.1)

func _on_button_touch_up(button: Button):
	"""Touch up feedback"""
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color.WHITE, 0.1)

func optimize_text_for_mobile(label: Label):
	"""Optimalizuje text pre mobilné zariadenia"""
	if not label:
		return
	
	var device_type = get_device_type()
	
	# Väčší font pre mobilné zariadenia
	if device_type in ["phone", "tablet"]:
		var font_size = label.get_theme_font_size("font_size")
		if font_size > 0:
			label.add_theme_font_size_override("font_size", int(font_size * MOBILE_FONT_SIZE_MULTIPLIER))
	
	# Automatické zalamovanie textu
	if label is RichTextLabel:
		var rich_label = label as RichTextLabel
		rich_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	elif label.autowrap_mode == TextServer.AUTOWRAP_OFF:
		label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

func create_mobile_safe_area(control: Control) -> Control:
	"""Vytvorí safe area pre mobilné zariadenia"""
	var safe_area = MarginContainer.new()
	safe_area.name = "MobileSafeArea"
	
	var device_type = get_device_type()
	var margin = 20
	
	match device_type:
		"phone":
			margin = 30  # Väčší margin pre telefóny
		"tablet":
			margin = 25
		_:
			margin = 20
	
	safe_area.add_theme_constant_override("margin_left", margin)
	safe_area.add_theme_constant_override("margin_right", margin)
	safe_area.add_theme_constant_override("margin_top", margin)
	safe_area.add_theme_constant_override("margin_bottom", margin)
	
	return safe_area

func enable_mobile_scrolling(scroll_container: ScrollContainer):
	"""Povolí mobilné scrollovanie"""
	if not scroll_container:
		return
	
	# Povolenie touch scrollovania
	scroll_container.scroll_horizontal_enabled = true
	scroll_container.scroll_vertical_enabled = true
	
	# Nastavenie scroll sensitivity
	var device_type = get_device_type()
	if device_type in ["phone", "tablet"]:
		# Mobilné zariadenia potrebujú citlivejšie scrollovanie
		scroll_container.scroll_deadzone = 0

func optimize_scene_for_mobile(scene: Node):
	"""Optimalizuje celú scénu pre mobilné zariadenia"""
	print("📱 Optimizing scene for mobile: ", scene.name)
	
	# Rekurzívne optimalizovanie všetkých Control nodov
	_optimize_node_recursive(scene)

func _optimize_node_recursive(node: Node):
	"""Rekurzívne optimalizuje nody"""
	if node is Control:
		var control = node as Control
		
		if control is Button:
			optimize_control_for_mobile(control)
			add_touch_feedback(control)
		elif control is Label:
			optimize_text_for_mobile(control)
		elif control is ScrollContainer:
			enable_mobile_scrolling(control)
		elif control is GridContainer:
			optimize_control_for_mobile(control)
	
	# Rekurzívne pre všetky deti
	for child in node.get_children():
		_optimize_node_recursive(child)

# Utility funkcie
func is_mobile_device() -> bool:
	"""Kontroluje či je zariadenie mobilné"""
	return get_device_type() in ["phone", "tablet"]

func get_safe_area_insets() -> Rect2:
	"""Vráti safe area insets pre notch/home indicator"""
	# V Godot 4 môžeme použiť DisplayServer
	if DisplayServer.has_feature(DisplayServer.FEATURE_SCREEN_CAPTURE):
		# Implementácia pre skutočné mobilné zariadenia
		return Rect2(0, 0, 0, 0)
	else:
		# Simulácia pre desktop
		return Rect2(0, 44, 0, 34)  # Typické iOS safe area
