[gd_scene load_steps=4 format=3 uid="uid://test_inventory"]

[ext_resource type="Script" path="res://scripts/InventoryTestScene.gd" id="1_test_script"]
[ext_resource type="Theme" uid="uid://dor07h2n53sgh" path="res://themes/DarkTemplarTheme.tres" id="2_theme"]
[ext_resource type="PackedScene" uid="uid://bvn8qkqxqxqxq" path="res://scenes/MobileInventory.tscn" id="3_inventory"]

[node name="InventoryTestScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")
script = ExtResource("1_test_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.1, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -300.0
offset_right = 300.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "INVENTORY SYSTÉM - TEST"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="TestButtons" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="AddItemsButton" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "Pridať testové predmety"

[node name="ShowInventoryButton" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "Zobraziť inventory"

[node name="TestCombinationButton" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "Test kombinácie"

[node name="ClearInventoryButton" type="Button" parent="VBoxContainer/TestButtons"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "Vymazať inventory"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="InfoLabel" type="RichTextLabel" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3
text = "[center]Testovacia scéna pre inventory systém[/center]

Funkcie:
• Pridanie predmetov
• Zobrazenie inventory
• Test kombinácií
• Vymazanie inventory"

[node name="HSeparator3" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "Späť do hlavného menu"

[node name="MobileInventory" parent="." instance=ExtResource("3_inventory")]
visible = false
layout_mode = 1

[connection signal="pressed" from="VBoxContainer/TestButtons/AddItemsButton" to="." method="_on_add_items_button_pressed"]
[connection signal="pressed" from="VBoxContainer/TestButtons/ShowInventoryButton" to="." method="_on_show_inventory_button_pressed"]
[connection signal="pressed" from="VBoxContainer/TestButtons/TestCombinationButton" to="." method="_on_test_combination_button_pressed"]
[connection signal="pressed" from="VBoxContainer/TestButtons/ClearInventoryButton" to="." method="_on_clear_inventory_button_pressed"]
[connection signal="pressed" from="VBoxContainer/BackButton" to="." method="_on_back_button_pressed"]
