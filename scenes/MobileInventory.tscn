[gd_scene load_steps=4 format=3 uid="uid://wl1xb5rdv0f8"]

[ext_resource type="Script" path="res://scripts/MobileInventory.gd" id="1_mobile_inventory"]
[ext_resource type="Theme" uid="uid://dor07h2n53sgh" path="res://themes/DarkTemplarTheme.tres" id="2_theme"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/Frames/frame_01.png" id="3_frame"]

[node name="MobileInventory" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")
script = ExtResource("1_mobile_inventory")

[node name="InventoryBackground" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.8)

[node name="InventoryPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -400.0
offset_right = 300.0
offset_bottom = 400.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_frame")
patch_margin_left = 32
patch_margin_top = 32
patch_margin_right = 32
patch_margin_bottom = 32

[node name="VBoxContainer" type="VBoxContainer" parent="InventoryPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleContainer" type="HBoxContainer" parent="InventoryPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="InventoryTitle" type="Label" parent="InventoryPanel/VBoxContainer/TitleContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "INVENTORY"
horizontal_alignment = 1

[node name="CloseButton" type="Button" parent="InventoryPanel/VBoxContainer/TitleContainer"]
layout_mode = 2
size_flags_horizontal = 0
custom_minimum_size = Vector2(60, 60)
text = "✕"

[node name="HSeparator" type="HSeparator" parent="InventoryPanel/VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="InventoryPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ItemGrid" type="GridContainer" parent="InventoryPanel/VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 3

[node name="HSeparator2" type="HSeparator" parent="InventoryPanel/VBoxContainer"]
layout_mode = 2

[node name="ActionContainer" type="HBoxContainer" parent="InventoryPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 0

[node name="UseButton" type="Button" parent="InventoryPanel/VBoxContainer/ActionContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 60)
text = "POUŽIŤ"
disabled = true

[node name="CombineButton" type="Button" parent="InventoryPanel/VBoxContainer/ActionContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 60)
text = "KOMBINOVAŤ"
disabled = true

[node name="InfoButton" type="Button" parent="InventoryPanel/VBoxContainer/ActionContainer"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(0, 60)
text = "INFO"
disabled = true

[node name="ItemInfoPanel" type="NinePatchRect" parent="."]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -200.0
offset_right = 250.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("3_frame")
patch_margin_left = 32
patch_margin_top = 32
patch_margin_right = 32
patch_margin_bottom = 32

[node name="InfoVBox" type="VBoxContainer" parent="ItemInfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="InfoTitle" type="Label" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
text = "INFORMÁCIE O PREDMETE"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2

[node name="ItemIcon" type="TextureRect" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
size_flags_horizontal = 4
custom_minimum_size = Vector2(128, 128)
expand_mode = 1
stretch_mode = 5

[node name="ItemName" type="Label" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
text = "Názov predmetu"
horizontal_alignment = 1

[node name="ItemDescription" type="Label" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
size_flags_vertical = 3
text = "Popis predmetu"
autowrap_mode = 3
vertical_alignment = 1

[node name="ItemUsage" type="Label" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
text = "Použitie: ..."
autowrap_mode = 3

[node name="HSeparator2" type="HSeparator" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2

[node name="CloseInfoButton" type="Button" parent="ItemInfoPanel/InfoVBox"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)
text = "ZAVRIEŤ"

[connection signal="pressed" from="InventoryPanel/VBoxContainer/TitleContainer/CloseButton" to="." method="_on_close_button_pressed"]
[connection signal="pressed" from="InventoryPanel/VBoxContainer/ActionContainer/UseButton" to="." method="_on_use_button_pressed"]
[connection signal="pressed" from="InventoryPanel/VBoxContainer/ActionContainer/CombineButton" to="." method="_on_combine_button_pressed"]
[connection signal="pressed" from="InventoryPanel/VBoxContainer/ActionContainer/InfoButton" to="." method="_on_info_button_pressed"]
[connection signal="pressed" from="ItemInfoPanel/InfoVBox/CloseInfoButton" to="." method="_on_close_info_button_pressed"]
